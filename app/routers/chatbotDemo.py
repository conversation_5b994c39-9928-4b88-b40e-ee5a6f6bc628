import os
from dotenv import load_dotenv
from fastapi import APIRouter, Request
from pydantic import BaseModel, constr, Field, model_validator
from typing import List, Dict, Optional
import aiohttp
from openai import OpenAI
from app.prompts.testPrompt import SystemPrompt

load_dotenv()

router = APIRouter(
    prefix="/api",
    tags=["chatbotDemo"],
)


class Message(BaseModel):
    role: str  # 限制为 "user" 或 "assistant"
    content: constr(min_length=1, max_length=200)

    @classmethod
    def check_role(cls, values):
        if values.get('role') not in ["user", "assistant"]:
            raise ValueError('role must be "user" or "assistant"')
        return values

class chatMessage(BaseModel):
    current_message: constr(min_length=1, max_length=200)
    chat_history: Optional[List[Message]] = None

# 获取API密钥
OPENROUTER_API_KEY = os.getenv("OPEN_ROUTER_API_KEY")


@router.post("/chatbot")
async def chatbot(chat_message: chatMessage):
    try:
        client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=f"{OPENROUTER_API_KEY}",
        )

        completion = client.chat.completions.create(
            extra_headers={
                "HTTP-Referer": "<YOUR_SITE_URL>",  # Optional. Site URL for rankings on openrouter.ai.
                "X-Title": "<YOUR_SITE_NAME>",  # Optional. Site title for rankings on openrouter.ai.
            },
            extra_body={},
            model="deepseek/deepseek-chat-v3.1",
            messages=[
                {
                    "system prompt": SystemPrompt,
                    "role": "user",
                    "content": chat_message.current_message,
                    "history": chat_message.chat_history
                }
            ]
        )
        return {
            "message": "Chatbot response received",
            "completion": completion.choices[0].message.content
        }
    except Exception as e:
        print(f"Error: {str(e)}")
        return {
            "error": "Internal server error",
            "details": str(e)
        }