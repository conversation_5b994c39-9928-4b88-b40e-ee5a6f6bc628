import json
import os
import ssl
from dotenv import load_dotenv
from fastapi import APIRouter, Request
from pydantic import BaseModel, constr, <PERSON>, model_validator
from typing import Optional
import aiohttp
import asyncio

load_dotenv()


class SystemPromptInfo(BaseModel):
    subject: constr(min_length=1, max_length=200)
    chatbot_type: constr(min_length=5, max_length=50)
    student_level: constr(min_length=10, max_length=200)
    primary_learning_goals: constr(min_length=1, max_length=200)
    coach_style: constr(min_length=10, max_length=200)
    Common_Challenges_and_Solutions: str = Field(default="未指定，建议以通用学习场景", alias="common_challenges_and_solutions")
    Extra_Considerations: str = Field(default="未指定，建议以通用学习场景", alias="extra_considerations")

class ModifyPromptInfo(BaseModel):
    chatbot_name: constr(min_length=1, max_length=50)
    welcome_prompt: constr(min_length=1, max_length=2000)
    system_prompt: constr(min_length=1, max_length=2000)
    chatbot_name_modification: Optional[str] = None
    system_prompt_modification: Optional[str] = None
    welcome_prompt_modification:  Optional[str] = None

    @model_validator(mode='before')
    @classmethod
    def check_at_least_one_modification(cls, values):
        if isinstance(values, dict):
            modifications = [
                values.get('chatbot_name_modification'),
                values.get('system_prompt_modification'),
                values.get('welcome_prompt_modification')
            ]
            if all(mod is None or mod.strip() == '' for mod in modifications):
                raise ValueError('At least one modification field must be non-empty')
        return values

router = APIRouter(
    prefix="/api",
    tags=["systemPrompt"],
)

# 获取API密钥
OPENROUTER_API_KEY = os.getenv("OPEN_ROUTER_API_KEY")


async def ai_generate_prompt(prompt_info: SystemPromptInfo):
    """使用AI生成系统提示"""
    prompt_text = f"""You are a professional chatbot design assistant specializing in educational contexts. Using the provided information, create a chatbot configuration for a learning assistant tailored to the specified subject and student needs:
    
-subject: {prompt_info.subject}
-chatbot_type: {prompt_info.chatbot_type}
-student_level: {prompt_info.student_level}
-primary_learning_goals: {prompt_info.primary_learning_goals}
-coach_style: {prompt_info.coach_style}
-Common_Challenges_and_Solutions: {prompt_info.Common_Challenges_and_Solutions}
-Extra_Considerations: {prompt_info.Extra_Considerations}



Generate a JSON object with the following fields:
- name：A concise name (max 20 characters) reflecting the subject and learning goals.
- welcome_prompt：An engaging welcome message (50-100 words) matching the coach_style, tailored to the student_level, and motivating students to pursue the primary_learning_goals.
- system_prompt：A detailed system prompt (100-200 words) defining the chatbot’s role, tone (aligned with coach_style), focus on primary_learning_goals, feedback style, and strategies to address Common_Challenges_and_Solutions. Incorporate Extra_Considerations to enhance personalization and effectiveness.

Ensure the tone is encouraging, the language is clear and age-appropriate, and the output avoids jargon unless suited to the student_level. Format the output as:
```json
{{
    "chatbot_name": "<chatbot name>",
    "welcome_prompt": "<welcome prompt>",
    "system_prompt": "<system prompt>"
}}
```"""

    payload = {
        "model": "x-ai/grok-3",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt_text
                    }
                ]
            }
        ],
        "response_format": {
            "type": "json_object"
        }
    }

    # # 创建一个SSL上下文，禁用证书验证
    # ssl_context = ssl.create_default_context()
    # ssl_context.check_hostname = False
    # ssl_context.verify_mode = ssl.CERT_NONE
    #
    # # 使用自定义SSL上下文创建ClientSession
    # conn = aiohttp.TCPConnector(ssl=ssl_context)
    async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=False)) as session:
        async with session.post(
            url="https://openrouter.ai/api/v1/chat/completions",
            headers={
                "Authorization": f"Bearer {OPENROUTER_API_KEY}",
                "Content-Type": "application/json",
            },
            json=payload
        ) as response:
            return response, await response.text()


async def ai_modify_prompt(modify_prompt_info: ModifyPromptInfo):
    """使用AI生成系统提示"""
    prompt_text = f"""You are an AI assistant tasked with modifying a chatbot's configuration based on provided inputs and modification instructions. You will receive a JSON object containing the following fields:
    
-chatbot_name: {modify_prompt_info.chatbot_name}
-welcome_prompt: {modify_prompt_info.welcome_prompt}
-system_prompt: {modify_prompt_info.system_prompt}
-chatbot_name_modification_request: {modify_prompt_info.chatbot_name_modification if modify_prompt_info.chatbot_name_modification else "未提供修改请求，无需修改"}
-system_prompt_modification_request: {modify_prompt_info.system_prompt_modification if modify_prompt_info.system_prompt_modification else "未提供修改请求，无需修改"}
-welcome_prompt_modification_request: {modify_prompt_info.welcome_prompt_modification if modify_prompt_info.welcome_prompt_modification else "未提供修改请求，无需修改"}



Generate a JSON object with the following fields:
- name：A concise name (max 20 characters) reflecting the subject and learning goals.
- welcome_prompt：An engaging welcome message (50-100 words) matching the coach_style, tailored to the student_level, and motivating students to pursue the primary_learning_goals.
- system_prompt：A detailed system prompt (100-200 words) defining the chatbot’s role, tone (aligned with coach_style), focus on primary_learning_goals, feedback style, and strategies to address Common_Challenges_and_Solutions. Incorporate Extra_Considerations to enhance personalization and effectiveness.

Ensure the tone is encouraging, the language is clear and age-appropriate, and the output avoids jargon unless suited to the student_level. Format the output as:
```json
{{
    "chatbot_name": "<chatbot name>",
    "welcome_prompt": "<welcome prompt>",
    "system_prompt": "<system prompt>"
}}
```"""

    payload = {
        "model": "x-ai/grok-3",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt_text
                    }
                ]
            }
        ],
        "response_format": {
            "type": "json_object"
        }
    }

    async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=False)) as session:
        async with session.post(
            url="https://openrouter.ai/api/v1/chat/completions",
            headers={
                "Authorization": f"Bearer {OPENROUTER_API_KEY}",
                "Content-Type": "application/json",
            },
            json=payload
        ) as response:
            return response, await response.text()


@router.post("/systemPromptInfo")
async def post_prompt(prompt_info: SystemPromptInfo):
    try:
        ai_response, response_text = await ai_generate_prompt(prompt_info)
        if ai_response.status != 200:
            return {
                "error": f"AI API error: {ai_response.status}",
                "details": response_text
            }

        response_json = json.loads(response_text)
        response_content = response_json["choices"][0]["message"]["content"]
        gen_info = json.loads(response_content)

        return {
            "message": "System prompt info received",
            "genInfo": gen_info,
        }
    except Exception as e:
        print(f"Error: {str(e)}")
        return {
            "error": "Internal server error",
            "details": str(e)
        }


@router.post("/modifyPromptInfo")
async def post_prompt(modify_prompt_info: ModifyPromptInfo):
    try:
        ai_response, response_text = await ai_modify_prompt(modify_prompt_info)
        if ai_response.status != 200:
            return {
                "error": f"AI API error: {ai_response.status}",
                "details": response_text
            }

        response_json = json.loads(response_text)
        response_content = response_json["choices"][0]["message"]["content"]
        gen_info = json.loads(response_content)

        return {
            "message": "Modify Request received",
            "genInfo": gen_info,
        }
    except Exception as e:
        print(f"Error: {str(e)}")
        return {
            "error": "Internal server error",
            "details": str(e)
        }