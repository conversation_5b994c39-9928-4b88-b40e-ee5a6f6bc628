from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from app.routers import (
    systemPrompt,
    chatbotDemo,
)

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Hello World"}

app.include_router(systemPrompt.router)
app.include_router(chatbotDemo.router)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)